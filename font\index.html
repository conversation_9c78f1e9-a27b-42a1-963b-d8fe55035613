<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>算命</title>
    <script>
      // 移动端适配：动态设置根字体大小
      (function () {
        function setRootFontSize() {
          const deviceWidth =
            document.documentElement.clientWidth || window.innerWidth;
          const fontSize = Math.min(deviceWidth / 10, 50); // 最大50px，防止字体过大
          document.documentElement.style.fontSize = fontSize + "px";
        }

        setRootFontSize();
        window.addEventListener("resize", setRootFontSize);
        window.addEventListener("orientationchange", setRootFontSize);
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
