import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import tailwindcss from "@tailwindcss/vite";
import { fileURLToPath, URL } from "node:url";
import { VantResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import path from "path";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import AutoImport from "unplugin-auto-import/vite";
import pxtorem from "postcss-pxtorem";

// 当前工作目录路径
const root: string = process.cwd();

export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    // vant 组件自动按需引入
    Components({
      dts: "src/typings/components.d.ts",
      resolvers: [VantResolver()]
    }),
    AutoImport({
      imports: ["vue", "vue-router", "pinia", "vue-i18n"],
      dirs: ["src/stores", "src/components", "src/hooks"],
      dts: "src/typings/auto-import.d.ts"
    }),
    // svg icon
    createSvgIconsPlugin({
      // 指定图标文件夹
      iconDirs: [path.resolve(root, "src/icons/svg")],
      // 指定 symbolId 格式
      symbolId: "icon-[dir]-[name]"
    })
  ],
  css: {
    postcss: {
      plugins: [
        pxtorem({
          rootValue: 37.5, // 设计稿宽度/10，假设设计稿是375px
          propList: ["*"], // 需要转换的属性，*表示所有
          exclude: /node_modules/i, // 排除node_modules
          selectorBlackList: [".van-"], // 排除vant组件
          minPixelValue: 2 // 小于2px的不转换
        })
      ]
    }
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url))
    }
  }
});
